# FORMATION PYTHON - 20 HEURES
## Programme de Formation Algorithmique et Python
### Conforme au Programme Académique Tunisien

---

## **INFORMATIONS GÉNÉRALES**

**Durée totale :** 20 heures  
**Public cible :** Débutants en programmation  
**Prérequis :** Notions de base en mathématiques  
**Objectif :** Maîtriser les concepts algorithmiques fondamentaux et leur implémentation en Python

---

## **STRUCTURE DE LA FORMATION**

### **PHASE 1 : ALGORITHMIQUE DE BASE (8 heures)**

#### **Séance 1 : Introduction à l'Algorithmique (2h)**
- **Objectifs :**
  - Comprendre les concepts de base de l'algorithmique
  - Distinguer algorithme, programme et langage de programmation
  
- **Contenu :**
  - Définition d'un algorithme
  - Caractéristiques d'un bon algorithme
  - Représentation des algorithmes (organigramme, pseudo-code)
  - Notion de complexité algorithmique (introduction)
  
- **Exercices pratiques :**
  - Création d'organigrammes simples
  - Écriture de pseudo-code pour des problèmes basiques

#### **Séance 2 : Variables et Types de Données (2h)**
- **Objectifs :**
  - Maîtriser la notion de variable
  - Comprendre les différents types de données
  
- **Contenu :**
  - Déclaration et affectation de variables
  - Types de données : entier, réel, caractère, chaîne, booléen
  - Opérations arithmétiques et logiques
  - Priorité des opérateurs
  
- **Exercices pratiques :**
  - Calculs mathématiques simples
  - Manipulation de chaînes de caractères

#### **Séance 3 : Structures de Contrôle - Conditions (2h)**
- **Objectifs :**
  - Maîtriser les structures conditionnelles
  - Implémenter la logique de décision
  
- **Contenu :**
  - Structure SI...ALORS...SINON
  - Conditions composées (ET, OU, NON)
  - Structures conditionnelles imbriquées
  - Structure SELON (switch/case)
  
- **Exercices pratiques :**
  - Algorithmes de tri et de classification
  - Résolution de problèmes avec conditions multiples

#### **Séance 4 : Structures de Contrôle - Boucles (2h)**
- **Objectifs :**
  - Maîtriser les structures itératives
  - Optimiser les algorithmes répétitifs
  
- **Contenu :**
  - Boucle POUR (for)
  - Boucle TANT QUE (while)
  - Boucle RÉPÉTER...JUSQU'À
  - Boucles imbriquées
  - Contrôle de boucles (break, continue)
  
- **Exercices pratiques :**
  - Calculs de sommes et moyennes
  - Algorithmes de recherche séquentielle

### **PHASE 2 : STRUCTURES DE DONNÉES (4 heures)**

#### **Séance 5 : Tableaux et Listes (2h)**
- **Objectifs :**
  - Comprendre et manipuler les structures de données linéaires
  
- **Contenu :**
  - Déclaration et initialisation de tableaux
  - Parcours de tableaux
  - Recherche dans un tableau
  - Tri de tableaux (tri à bulles, tri par sélection)
  
- **Exercices pratiques :**
  - Gestion d'une liste d'étudiants
  - Algorithmes de tri et de recherche

#### **Séance 6 : Fonctions et Procédures (2h)**
- **Objectifs :**
  - Structurer le code avec des fonctions
  - Comprendre la modularité
  
- **Contenu :**
  - Définition et appel de fonctions
  - Paramètres et valeurs de retour
  - Portée des variables (locale/globale)
  - Récursivité (introduction)
  
- **Exercices pratiques :**
  - Création de bibliothèques de fonctions
  - Résolution de problèmes par décomposition

### **PHASE 3 : IMPLÉMENTATION EN PYTHON (8 heures)**

#### **Séance 7 : Introduction à Python (2h)**
- **Objectifs :**
  - Découvrir l'environnement Python
  - Transposer les concepts algorithmiques en Python
  
- **Contenu :**
  - Installation et configuration de Python
  - Environnement de développement (IDLE, VS Code)
  - Syntaxe de base Python
  - Variables et types de données en Python
  - Opérateurs en Python
  
- **Exercices pratiques :**
  - Premier programme Python
  - Conversion d'algorithmes simples en Python

#### **Séance 8 : Structures de Contrôle en Python (2h)**
- **Objectifs :**
  - Implémenter les structures conditionnelles et itératives
  
- **Contenu :**
  - Instructions if, elif, else
  - Boucles for et while en Python
  - Compréhensions de listes (introduction)
  - Gestion des exceptions (try/except)
  
- **Exercices pratiques :**
  - Programmes avec conditions complexes
  - Algorithmes itératifs en Python

#### **Séance 9 : Structures de Données Python (2h)**
- **Objectifs :**
  - Maîtriser les structures de données Python
  
- **Contenu :**
  - Listes, tuples, dictionnaires, ensembles
  - Méthodes et opérations sur les structures
  - Chaînes de caractères avancées
  - Fichiers texte (lecture/écriture)
  
- **Exercices pratiques :**
  - Gestion de données avec dictionnaires
  - Traitement de fichiers texte

#### **Séance 10 : Fonctions et Modules Python (2h)**
- **Objectifs :**
  - Créer et utiliser des fonctions Python
  - Organiser le code en modules
  
- **Contenu :**
  - Définition de fonctions en Python
  - Arguments par défaut et arguments nommés
  - Fonctions lambda
  - Modules et packages
  - Bibliothèques standard Python
  
- **Exercices pratiques :**
  - Création d'un module de fonctions utilitaires
  - Utilisation de bibliothèques (math, random, datetime)

---

## **ÉVALUATION ET PROJETS**

### **Évaluations Continues :**
- Quiz algorithmique (Séance 4) - 20%
- Projet Python intermédiaire (Séance 8) - 30%
- Projet final (Séance 10) - 50%

### **Projet Final Suggéré :**
**Système de Gestion d'une Bibliothèque**
- Gestion des livres (ajout, suppression, recherche)
- Gestion des emprunts
- Statistiques et rapports
- Interface utilisateur en mode console

---

## **RESSOURCES PÉDAGOGIQUES**

### **Manuels de Référence :**
- "Algorithmique et Programmation" - Programme Tunisien
- "Python pour les Débutants" - Documentation officielle
- Exercices du Baccalauréat Tunisien (Section Informatique)

### **Outils Nécessaires :**
- Python 3.8+ (dernière version stable)
- Éditeur de code (VS Code, PyCharm Community, ou IDLE)
- Accès internet pour documentation

### **Supports de Cours :**
- Présentations PowerPoint pour chaque séance
- Fiches d'exercices avec corrections
- Projets guidés avec solutions

---

## **PLANNING DÉTAILLÉ**

| Semaine | Séance | Durée | Contenu | Type |
|---------|--------|-------|---------|------|
| 1 | 1 | 2h | Introduction Algorithmique | Théorie + TP |
| 1 | 2 | 2h | Variables et Types | Théorie + TP |
| 2 | 3 | 2h | Structures Conditionnelles | Théorie + TP |
| 2 | 4 | 2h | Structures Itératives | Théorie + TP |
| 3 | 5 | 2h | Tableaux et Listes | Théorie + TP |
| 3 | 6 | 2h | Fonctions et Procédures | Théorie + TP |
| 4 | 7 | 2h | Introduction Python | Théorie + TP |
| 4 | 8 | 2h | Contrôle en Python | Théorie + TP |
| 5 | 9 | 2h | Structures Python | Théorie + TP |
| 5 | 10 | 2h | Fonctions Python + Projet | TP + Évaluation |

---

## **CRITÈRES DE RÉUSSITE**

### **Compétences Algorithmiques :**
- [ ] Analyser un problème et concevoir un algorithme
- [ ] Utiliser les structures de contrôle appropriées
- [ ] Optimiser la complexité des algorithmes
- [ ] Documenter et commenter le code

### **Compétences Python :**
- [ ] Écrire du code Python syntaxiquement correct
- [ ] Utiliser les structures de données appropriées
- [ ] Créer et utiliser des fonctions
- [ ] Déboguer et tester le code

### **Compétences Transversales :**
- [ ] Résoudre des problèmes de manière méthodique
- [ ] Travailler en équipe sur des projets
- [ ] Présenter et expliquer ses solutions
- [ ] Rechercher et utiliser la documentation

---

**Document préparé conformément au programme académique tunisien**  
**Version 1.0 - Formation Python 20h**
