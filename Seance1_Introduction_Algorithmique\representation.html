<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Représentation des Algorithmes</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .flowchart-container {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            text-align: center;
        }
        
        .flowchart-symbols {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .symbol-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .symbol-shape {
            width: 80px;
            height: 60px;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            border-radius: 5px;
        }
        
        .start-end { background: #e74c3c; border-radius: 30px; }
        .process { background: #3498db; }
        .decision { background: #f39c12; transform: rotate(45deg); }
        .decision span { transform: rotate(-45deg); }
        .input-output { background: #27ae60; clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%); }
        
        .pseudocode-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 2rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            text-align: left;
            margin: 2rem 0;
            line-height: 1.8;
        }
        
        .algorithm-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .comparison-item {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            border-left: 5px solid #3498db;
        }
        
        .interactive-example {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
        }
        
        .step-by-step {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #f1c40f;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1><i class="fas fa-sitemap"></i> Représentation des Algorithmes</h1>
            <h2>Organigrammes et Pseudo-code</h2>
            <div class="session-info">
                <span><i class="fas fa-clock"></i> Partie 2/4</span>
                <span><i class="fas fa-chart-flow"></i> Méthodes Visuelles</span>
            </div>
        </div>
    </header>

    <nav class="navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link"><i class="fas fa-home"></i> Accueil</a></li>
                <li><a href="#organigrammes" class="nav-link"><i class="fas fa-project-diagram"></i> Organigrammes</a></li>
                <li><a href="#pseudocode" class="nav-link"><i class="fas fa-code"></i> Pseudo-code</a></li>
                <li><a href="#exemples" class="nav-link"><i class="fas fa-lightbulb"></i> Exemples</a></li>
                <li><a href="#exercices" class="nav-link"><i class="fas fa-pencil-alt"></i> Exercices</a></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <!-- Section Organigrammes -->
            <section id="organigrammes" class="content-section">
                <h2><i class="fas fa-project-diagram"></i> Les Organigrammes (Flowcharts)</h2>
                
                <div class="definition-box">
                    <h3><i class="fas fa-info-circle"></i> Définition</h3>
                    <p class="definition-text">
                        Un <strong>organigramme</strong> est une représentation graphique d'un algorithme 
                        utilisant des symboles géométriques reliés par des flèches pour montrer le flux d'exécution.
                    </p>
                </div>

                <h3><i class="fas fa-shapes"></i> Symboles Standard des Organigrammes</h3>
                <div class="flowchart-symbols">
                    <div class="symbol-card">
                        <div class="symbol-shape start-end">
                            <span>Début</span>
                        </div>
                        <h4>Début/Fin</h4>
                        <p>Ovale - Marque le début et la fin de l'algorithme</p>
                    </div>
                    
                    <div class="symbol-card">
                        <div class="symbol-shape process">
                            <span>Action</span>
                        </div>
                        <h4>Traitement</h4>
                        <p>Rectangle - Représente une action ou un calcul</p>
                    </div>
                    
                    <div class="symbol-card">
                        <div class="symbol-shape decision">
                            <span>?</span>
                        </div>
                        <h4>Décision</h4>
                        <p>Losange - Représente un test ou une condition</p>
                    </div>
                    
                    <div class="symbol-card">
                        <div class="symbol-shape input-output">
                            <span>E/S</span>
                        </div>
                        <h4>Entrée/Sortie</h4>
                        <p>Parallélogramme - Lecture ou affichage de données</p>
                    </div>
                </div>

                <div class="flowchart-container">
                    <h3><i class="fas fa-eye"></i> Exemple d'Organigramme : Calcul de la Moyenne</h3>
                    <div style="font-family: monospace; line-height: 2;">
                        <div style="background: #e74c3c; color: white; padding: 10px; border-radius: 20px; display: inline-block; margin: 10px;">
                            DÉBUT
                        </div>
                        <br>↓<br>
                        <div style="background: #27ae60; color: white; padding: 10px; clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%); display: inline-block; margin: 10px;">
                            Lire note1, note2
                        </div>
                        <br>↓<br>
                        <div style="background: #3498db; color: white; padding: 10px; display: inline-block; margin: 10px;">
                            moyenne = (note1 + note2) / 2
                        </div>
                        <br>↓<br>
                        <div style="background: #27ae60; color: white; padding: 10px; clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%); display: inline-block; margin: 10px;">
                            Afficher moyenne
                        </div>
                        <br>↓<br>
                        <div style="background: #e74c3c; color: white; padding: 10px; border-radius: 20px; display: inline-block; margin: 10px;">
                            FIN
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Pseudo-code -->
            <section id="pseudocode" class="content-section">
                <h2><i class="fas fa-code"></i> Le Pseudo-code</h2>
                
                <div class="definition-box">
                    <h3><i class="fas fa-info-circle"></i> Définition</h3>
                    <p class="definition-text">
                        Le <strong>pseudo-code</strong> est une description textuelle d'un algorithme 
                        utilisant un langage proche du langage naturel mais structuré comme un programme.
                    </p>
                </div>

                <h3><i class="fas fa-list"></i> Règles du Pseudo-code</h3>
                <div class="characteristics-list">
                    <div class="characteristic-item">
                        <div class="char-icon">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="char-content">
                            <h3>Structure</h3>
                            <p>Utiliser l'indentation pour montrer la hiérarchie</p>
                            <div class="char-example">
                                <strong>Exemple :</strong> Les instructions dans une condition sont indentées
                            </div>
                        </div>
                    </div>

                    <div class="characteristic-item">
                        <div class="char-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <div class="char-content">
                            <h3>Langage Simple</h3>
                            <p>Utiliser des mots clés simples et compréhensibles</p>
                            <div class="char-example">
                                <strong>Exemple :</strong> "LIRE", "AFFICHER", "SI...ALORS...SINON"
                            </div>
                        </div>
                    </div>

                    <div class="characteristic-item">
                        <div class="char-icon">
                            <i class="fas fa-equals"></i>
                        </div>
                        <div class="char-content">
                            <h3>Affectation</h3>
                            <p>Utiliser ← ou = pour l'affectation de variables</p>
                            <div class="char-example">
                                <strong>Exemple :</strong> x ← 5 ou x = 5
                            </div>
                        </div>
                    </div>
                </div>

                <h3><i class="fas fa-keyboard"></i> Mots-clés Standard</h3>
                <div class="pseudocode-example">
DÉBUT
    // Déclaration de variables
    VARIABLE nom : chaîne
    VARIABLE age : entier
    
    // Entrée de données
    LIRE nom
    LIRE age
    
    // Traitement conditionnel
    SI age >= 18 ALORS
        AFFICHER nom + " est majeur"
    SINON
        AFFICHER nom + " est mineur"
    FIN SI
    
    // Boucle
    POUR i DE 1 À 5 FAIRE
        AFFICHER "Compteur : " + i
    FIN POUR
FIN
                </div>
            </section>

            <!-- Section Exemples -->
            <section id="exemples" class="content-section">
                <h2><i class="fas fa-lightbulb"></i> Exemples Pratiques</h2>
                
                <div class="algorithm-comparison">
                    <div class="comparison-item">
                        <h3><i class="fas fa-project-diagram"></i> Organigramme</h3>
                        <h4>Vérifier si un nombre est pair</h4>
                        <div style="text-align: center; font-family: monospace; line-height: 1.8;">
                            <div style="background: #e74c3c; color: white; padding: 8px; border-radius: 15px; display: inline-block; margin: 5px;">DÉBUT</div><br>
                            ↓<br>
                            <div style="background: #27ae60; color: white; padding: 8px; display: inline-block; margin: 5px;">Lire nombre</div><br>
                            ↓<br>
                            <div style="background: #f39c12; color: white; padding: 8px; transform: rotate(45deg); display: inline-block; margin: 5px;"><span style="transform: rotate(-45deg); display: inline-block;">nombre % 2 = 0?</span></div><br>
                            ↙ OUI &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; NON ↘<br>
                            <div style="background: #27ae60; color: white; padding: 8px; display: inline-block; margin: 5px;">Afficher "Pair"</div>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            <div style="background: #27ae60; color: white; padding: 8px; display: inline-block; margin: 5px;">Afficher "Impair"</div><br>
                            ↓<br>
                            <div style="background: #e74c3c; color: white; padding: 8px; border-radius: 15px; display: inline-block; margin: 5px;">FIN</div>
                        </div>
                    </div>
                    
                    <div class="comparison-item">
                        <h3><i class="fas fa-code"></i> Pseudo-code</h3>
                        <h4>Vérifier si un nombre est pair</h4>
                        <div class="pseudocode-example" style="background: #34495e; font-size: 0.9rem;">
DÉBUT
    VARIABLE nombre : entier
    
    AFFICHER "Entrez un nombre : "
    LIRE nombre
    
    SI nombre % 2 = 0 ALORS
        AFFICHER "Le nombre est pair"
    SINON
        AFFICHER "Le nombre est impair"
    FIN SI
FIN
                        </div>
                    </div>
                </div>

                <div class="interactive-example">
                    <h3><i class="fas fa-play"></i> Exemple Interactif : Calcul de Factorielle</h3>
                    <p>Suivons étape par étape l'algorithme de calcul de factorielle de 4 :</p>
                    
                    <div class="step-by-step">
                        <div class="step">
                            <strong>Étape 1 :</strong> Initialisation - n = 4, factorielle = 1
                        </div>
                        <div class="step">
                            <strong>Étape 2 :</strong> i = 1, factorielle = 1 × 1 = 1
                        </div>
                        <div class="step">
                            <strong>Étape 3 :</strong> i = 2, factorielle = 1 × 2 = 2
                        </div>
                        <div class="step">
                            <strong>Étape 4 :</strong> i = 3, factorielle = 2 × 3 = 6
                        </div>
                        <div class="step">
                            <strong>Étape 5 :</strong> i = 4, factorielle = 6 × 4 = 24
                        </div>
                        <div class="step">
                            <strong>Résultat :</strong> 4! = 24
                        </div>
                    </div>
                </div>
            </section>

            <!-- Navigation -->
            <div class="page-navigation">
                <a href="index.html" class="nav-button" style="background: linear-gradient(135deg, #95a5a6, #7f8c8d); margin-right: 1rem;">
                    <i class="fas fa-arrow-left"></i>
                    <span>Précédent : Introduction</span>
                </a>
                <a href="complexite.html" class="nav-button">
                    <span>Suivant : Complexité</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Formation Python - Programme Académique Tunisien</p>
            <p>Séance 1 : Représentation des Algorithmes</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
