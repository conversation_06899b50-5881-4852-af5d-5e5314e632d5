<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Séance 1 : Introduction à l'Algorithmique</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <header class="header">
        <div class="container">
            <h1><i class="fas fa-code"></i> Formation Python - Séance 1</h1>
            <h2>Introduction à l'Algorithmique</h2>
            <div class="session-info">
                <span><i class="fas fa-clock"></i> Durée: 2 heures</span>
                <span><i class="fas fa-users"></i> Niveau: Débutant</span>
                <span><i class="fas fa-flag"></i> Programme Tunisien</span>
            </div>
        </div>
    </header>

    <nav class="navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="#objectifs" class="nav-link"><i class="fas fa-target"></i> Objectifs</a></li>
                <li><a href="#definition" class="nav-link"><i class="fas fa-book"></i> Définition</a></li>
                <li><a href="#caracteristiques" class="nav-link"><i class="fas fa-check-circle"></i> Caractéristiques</a></li>
                <li><a href="#representation" class="nav-link"><i class="fas fa-sitemap"></i> Représentation</a></li>
                <li><a href="#complexite" class="nav-link"><i class="fas fa-chart-line"></i> Complexité</a></li>
                <li><a href="#exercices" class="nav-link"><i class="fas fa-pencil-alt"></i> Exercices</a></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <!-- Section Objectifs -->
            <section id="objectifs" class="content-section">
                <h2><i class="fas fa-target"></i> Objectifs de la Séance</h2>
                <div class="objectives-grid">
                    <div class="objective-card">
                        <i class="fas fa-lightbulb"></i>
                        <h3>Comprendre</h3>
                        <p>Les concepts de base de l'algorithmique et son importance en programmation</p>
                    </div>
                    <div class="objective-card">
                        <i class="fas fa-puzzle-piece"></i>
                        <h3>Distinguer</h3>
                        <p>Algorithme, programme et langage de programmation</p>
                    </div>
                    <div class="objective-card">
                        <i class="fas fa-tools"></i>
                        <h3>Maîtriser</h3>
                        <p>Les méthodes de représentation des algorithmes</p>
                    </div>
                    <div class="objective-card">
                        <i class="fas fa-rocket"></i>
                        <h3>Appliquer</h3>
                        <p>Les concepts appris dans des exercices pratiques</p>
                    </div>
                </div>
            </section>

            <!-- Section Définition -->
            <section id="definition" class="content-section">
                <h2><i class="fas fa-book"></i> Qu'est-ce qu'un Algorithme ?</h2>
                
                <div class="definition-box">
                    <h3><i class="fas fa-quote-left"></i> Définition</h3>
                    <p class="definition-text">
                        Un <strong>algorithme</strong> est une suite finie et ordonnée d'instructions 
                        permettant de résoudre un problème ou d'effectuer une tâche donnée.
                    </p>
                </div>

                <div class="concept-comparison">
                    <div class="comparison-card">
                        <h4><i class="fas fa-brain"></i> Algorithme</h4>
                        <p>Description abstraite de la solution (indépendant du langage)</p>
                        <div class="example">
                            <strong>Exemple :</strong> "Pour faire du thé : chauffer l'eau, ajouter le thé, laisser infuser"
                        </div>
                    </div>
                    
                    <div class="comparison-card">
                        <h4><i class="fas fa-file-code"></i> Programme</h4>
                        <p>Traduction de l'algorithme dans un langage de programmation</p>
                        <div class="example">
                            <strong>Exemple :</strong> Code Python qui implémente l'algorithme
                        </div>
                    </div>
                    
                    <div class="comparison-card">
                        <h4><i class="fas fa-language"></i> Langage</h4>
                        <p>Outil de communication avec l'ordinateur (Python, Java, C++...)</p>
                        <div class="example">
                            <strong>Exemple :</strong> Python, JavaScript, C++
                        </div>
                    </div>
                </div>

                <div class="real-world-examples">
                    <h3><i class="fas fa-globe"></i> Exemples d'Algorithmes dans la Vie Quotidienne</h3>
                    <div class="examples-grid">
                        <div class="example-item">
                            <i class="fas fa-route"></i>
                            <h4>Navigation GPS</h4>
                            <p>Trouver le chemin le plus court entre deux points</p>
                        </div>
                        <div class="example-item">
                            <i class="fas fa-search"></i>
                            <h4>Moteur de Recherche</h4>
                            <p>Classer les résultats par pertinence</p>
                        </div>
                        <div class="example-item">
                            <i class="fas fa-heart"></i>
                            <h4>Réseaux Sociaux</h4>
                            <p>Suggérer des amis ou du contenu</p>
                        </div>
                        <div class="example-item">
                            <i class="fas fa-shopping-cart"></i>
                            <h4>E-commerce</h4>
                            <p>Recommander des produits</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Caractéristiques -->
            <section id="caracteristiques" class="content-section">
                <h2><i class="fas fa-check-circle"></i> Caractéristiques d'un Bon Algorithme</h2>
                
                <div class="characteristics-list">
                    <div class="characteristic-item">
                        <div class="char-icon">
                            <i class="fas fa-flag-checkered"></i>
                        </div>
                        <div class="char-content">
                            <h3>Finitude</h3>
                            <p>L'algorithme doit se terminer après un nombre fini d'étapes</p>
                            <div class="char-example">
                                <strong>Exemple :</strong> Une boucle qui compte de 1 à 10 (pas infinie)
                            </div>
                        </div>
                    </div>

                    <div class="characteristic-item">
                        <div class="char-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="char-content">
                            <h3>Clarté</h3>
                            <p>Chaque instruction doit être précise et non ambiguë</p>
                            <div class="char-example">
                                <strong>Exemple :</strong> "Ajouter 5" au lieu de "Ajouter un peu"
                            </div>
                        </div>
                    </div>

                    <div class="characteristic-item">
                        <div class="char-icon">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="char-content">
                            <h3>Déterminisme</h3>
                            <p>Même entrée → même résultat à chaque exécution</p>
                            <div class="char-example">
                                <strong>Exemple :</strong> 2 + 3 donne toujours 5
                            </div>
                        </div>
                    </div>

                    <div class="characteristic-item">
                        <div class="char-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="char-content">
                            <h3>Efficacité</h3>
                            <p>Utilise le minimum de ressources (temps, mémoire)</p>
                            <div class="char-example">
                                <strong>Exemple :</strong> Tri rapide vs tri à bulles
                            </div>
                        </div>
                    </div>

                    <div class="characteristic-item">
                        <div class="char-icon">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </div>
                        <div class="char-content">
                            <h3>Généralité</h3>
                            <p>Peut résoudre une classe de problèmes, pas un seul cas</p>
                            <div class="char-example">
                                <strong>Exemple :</strong> Tri de n'importe quelle liste de nombres
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Navigation vers les autres pages -->
            <div class="page-navigation">
                <a href="representation.html" class="nav-button next">
                    <span>Suivant : Représentation des Algorithmes</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Formation Python - Programme Académique Tunisien</p>
            <p>Séance 1 : Introduction à l'Algorithmique</p>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
