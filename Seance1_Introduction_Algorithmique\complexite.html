<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complexité Algorithmique</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .complexity-chart {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            text-align: center;
        }
        
        .complexity-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .complexity-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            border-left: 5px solid;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .complexity-card:hover {
            transform: translateY(-5px);
        }
        
        .o1 { border-left-color: #27ae60; }
        .on { border-left-color: #f39c12; }
        .on2 { border-left-color: #e74c3c; }
        .ologn { border-left-color: #3498db; }
        
        .performance-comparison {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .comparison-table th {
            background: rgba(255, 255, 255, 0.2);
            font-weight: bold;
        }
        
        .algorithm-analysis {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
        }
        
        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1.5rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            line-height: 1.6;
        }
        
        .complexity-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            margin: 0.5rem;
        }
        
        .badge-o1 { background: #27ae60; }
        .badge-on { background: #f39c12; }
        .badge-on2 { background: #e74c3c; }
        .badge-ologn { background: #3498db; }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1><i class="fas fa-chart-line"></i> Complexité Algorithmique</h1>
            <h2>Introduction à l'Analyse de Performance</h2>
            <div class="session-info">
                <span><i class="fas fa-clock"></i> Partie 3/4</span>
                <span><i class="fas fa-tachometer-alt"></i> Performance</span>
            </div>
        </div>
    </header>

    <nav class="navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link"><i class="fas fa-home"></i> Accueil</a></li>
                <li><a href="#introduction" class="nav-link"><i class="fas fa-info"></i> Introduction</a></li>
                <li><a href="#notations" class="nav-link"><i class="fas fa-calculator"></i> Notations</a></li>
                <li><a href="#exemples" class="nav-link"><i class="fas fa-code"></i> Exemples</a></li>
                <li><a href="#comparaison" class="nav-link"><i class="fas fa-balance-scale"></i> Comparaison</a></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <!-- Section Introduction -->
            <section id="introduction" class="content-section">
                <h2><i class="fas fa-info"></i> Qu'est-ce que la Complexité ?</h2>
                
                <div class="definition-box">
                    <h3><i class="fas fa-stopwatch"></i> Définition</h3>
                    <p class="definition-text">
                        La <strong>complexité algorithmique</strong> mesure la quantité de ressources 
                        (temps, mémoire) nécessaires à l'exécution d'un algorithme en fonction de la taille des données.
                    </p>
                </div>

                <div class="concept-comparison">
                    <div class="comparison-card">
                        <h4><i class="fas fa-clock"></i> Complexité Temporelle</h4>
                        <p>Mesure le temps d'exécution de l'algorithme</p>
                        <div class="example">
                            <strong>Question :</strong> Combien d'opérations pour traiter n éléments ?
                        </div>
                    </div>
                    
                    <div class="comparison-card">
                        <h4><i class="fas fa-memory"></i> Complexité Spatiale</h4>
                        <p>Mesure l'espace mémoire utilisé par l'algorithme</p>
                        <div class="example">
                            <strong>Question :</strong> Combien de mémoire pour stocker les données ?
                        </div>
                    </div>
                </div>

                <div class="real-world-examples">
                    <h3><i class="fas fa-question-circle"></i> Pourquoi c'est Important ?</h3>
                    <div class="examples-grid">
                        <div class="example-item">
                            <i class="fas fa-database"></i>
                            <h4>Grandes Données</h4>
                            <p>Traiter des millions d'enregistrements efficacement</p>
                        </div>
                        <div class="example-item">
                            <i class="fas fa-mobile-alt"></i>
                            <h4>Applications Mobiles</h4>
                            <p>Économiser la batterie et la mémoire</p>
                        </div>
                        <div class="example-item">
                            <i class="fas fa-server"></i>
                            <h4>Serveurs Web</h4>
                            <p>Répondre rapidement à de nombreux utilisateurs</p>
                        </div>
                        <div class="example-item">
                            <i class="fas fa-gamepad"></i>
                            <h4>Jeux Vidéo</h4>
                            <p>Maintenir 60 images par seconde</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Notations -->
            <section id="notations" class="content-section">
                <h2><i class="fas fa-calculator"></i> Notations de Complexité</h2>
                
                <p style="font-size: 1.1rem; margin-bottom: 2rem;">
                    On utilise la <strong>notation "Grand O"</strong> pour exprimer la complexité dans le pire des cas.
                </p>

                <div class="complexity-examples">
                    <div class="complexity-card o1">
                        <h3><i class="fas fa-rocket"></i> O(1) - Constante</h3>
                        <span class="complexity-badge badge-o1">Excellent</span>
                        <p><strong>Définition :</strong> Le temps d'exécution ne dépend pas de la taille des données</p>
                        <div class="code-example">
// Accès à un élément d'un tableau
tableau[5]  // Toujours instantané

// Affichage d'une valeur
AFFICHER "Bonjour"  // Temps constant
                        </div>
                        <p><strong>Exemple :</strong> Accéder au 5ème élément d'un tableau</p>
                    </div>

                    <div class="complexity-card ologn">
                        <h3><i class="fas fa-search"></i> O(log n) - Logarithmique</h3>
                        <span class="complexity-badge badge-ologn">Très bon</span>
                        <p><strong>Définition :</strong> Le temps augmente lentement avec la taille</p>
                        <div class="code-example">
// Recherche dichotomique
// Divise les données par 2 à chaque étape
TANT QUE début <= fin FAIRE
    milieu = (début + fin) / 2
    SI tableau[milieu] = valeur ALORS
        RETOURNER milieu
    ...
                        </div>
                        <p><strong>Exemple :</strong> Recherche dans un dictionnaire</p>
                    </div>

                    <div class="complexity-card on">
                        <h3><i class="fas fa-list"></i> O(n) - Linéaire</h3>
                        <span class="complexity-badge badge-on">Acceptable</span>
                        <p><strong>Définition :</strong> Le temps est proportionnel à la taille des données</p>
                        <div class="code-example">
// Parcours d'un tableau
POUR i DE 0 À n-1 FAIRE
    AFFICHER tableau[i]
FIN POUR

// Recherche séquentielle
POUR chaque élément FAIRE
    SI élément = valeur_cherchée ALORS
        RETOURNER position
                        </div>
                        <p><strong>Exemple :</strong> Lire tous les éléments d'une liste</p>
                    </div>

                    <div class="complexity-card on2">
                        <h3><i class="fas fa-exclamation-triangle"></i> O(n²) - Quadratique</h3>
                        <span class="complexity-badge badge-on2">Problématique</span>
                        <p><strong>Définition :</strong> Le temps augmente avec le carré de la taille</p>
                        <div class="code-example">
// Boucles imbriquées
POUR i DE 0 À n-1 FAIRE
    POUR j DE 0 À n-1 FAIRE
        // Comparer tableau[i] et tableau[j]
        SI tableau[i] > tableau[j] ALORS
            échanger(tableau[i], tableau[j])
    FIN POUR
FIN POUR
                        </div>
                        <p><strong>Exemple :</strong> Tri à bulles, comparaison de tous avec tous</p>
                    </div>
                </div>
            </section>

            <!-- Section Exemples -->
            <section id="exemples" class="content-section">
                <h2><i class="fas fa-code"></i> Analyse d'Algorithmes</h2>
                
                <div class="algorithm-analysis">
                    <h3><i class="fas fa-search"></i> Exemple 1 : Recherche d'un Élément</h3>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-top: 1rem;">
                        <div>
                            <h4>Recherche Séquentielle <span class="complexity-badge badge-on">O(n)</span></h4>
                            <div class="code-example">
FONCTION rechercheSequentielle(tableau, valeur)
    POUR i DE 0 À taille(tableau)-1 FAIRE
        SI tableau[i] = valeur ALORS
            RETOURNER i
        FIN SI
    FIN POUR
    RETOURNER -1  // Non trouvé
FIN FONCTION
                            </div>
                            <p><strong>Analyse :</strong> Dans le pire cas, on doit vérifier tous les éléments.</p>
                        </div>
                        
                        <div>
                            <h4>Recherche Dichotomique <span class="complexity-badge badge-ologn">O(log n)</span></h4>
                            <div class="code-example">
FONCTION rechercheDichotomique(tableau_trié, valeur)
    début = 0
    fin = taille(tableau) - 1
    
    TANT QUE début <= fin FAIRE
        milieu = (début + fin) / 2
        SI tableau[milieu] = valeur ALORS
            RETOURNER milieu
        SINON SI tableau[milieu] < valeur ALORS
            début = milieu + 1
        SINON
            fin = milieu - 1
        FIN SI
    FIN TANT QUE
    RETOURNER -1
FIN FONCTION
                            </div>
                            <p><strong>Analyse :</strong> On divise l'espace de recherche par 2 à chaque étape.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Comparaison -->
            <section id="comparaison" class="content-section">
                <h2><i class="fas fa-balance-scale"></i> Comparaison des Performances</h2>
                
                <div class="performance-comparison">
                    <h3><i class="fas fa-chart-bar"></i> Impact de la Taille des Données</h3>
                    <p>Nombre d'opérations nécessaires selon la complexité :</p>
                    
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Taille (n)</th>
                                <th>O(1)</th>
                                <th>O(log n)</th>
                                <th>O(n)</th>
                                <th>O(n²)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>10</td>
                                <td>1</td>
                                <td>3</td>
                                <td>10</td>
                                <td>100</td>
                            </tr>
                            <tr>
                                <td>100</td>
                                <td>1</td>
                                <td>7</td>
                                <td>100</td>
                                <td>10,000</td>
                            </tr>
                            <tr>
                                <td>1,000</td>
                                <td>1</td>
                                <td>10</td>
                                <td>1,000</td>
                                <td>1,000,000</td>
                            </tr>
                            <tr>
                                <td>10,000</td>
                                <td>1</td>
                                <td>13</td>
                                <td>10,000</td>
                                <td>100,000,000</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <p style="margin-top: 1rem; font-style: italic;">
                        <i class="fas fa-lightbulb"></i> 
                        <strong>Observation :</strong> La différence devient énorme avec de grandes données !
                    </p>
                </div>

                <div class="real-world-examples">
                    <h3><i class="fas fa-clock"></i> Temps d'Exécution Réels</h3>
                    <div class="examples-grid">
                        <div class="example-item">
                            <i class="fas fa-rocket" style="color: #27ae60;"></i>
                            <h4>O(1) - Instantané</h4>
                            <p>Moins d'1 milliseconde</p>
                        </div>
                        <div class="example-item">
                            <i class="fas fa-tachometer-alt" style="color: #3498db;"></i>
                            <h4>O(log n) - Très Rapide</h4>
                            <p>Quelques millisecondes</p>
                        </div>
                        <div class="example-item">
                            <i class="fas fa-walking" style="color: #f39c12;"></i>
                            <h4>O(n) - Raisonnable</h4>
                            <p>Secondes pour millions d'éléments</p>
                        </div>
                        <div class="example-item">
                            <i class="fas fa-snail" style="color: #e74c3c;"></i>
                            <h4>O(n²) - Très Lent</h4>
                            <p>Heures pour milliers d'éléments</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Navigation -->
            <div class="page-navigation">
                <a href="representation.html" class="nav-button" style="background: linear-gradient(135deg, #95a5a6, #7f8c8d); margin-right: 1rem;">
                    <i class="fas fa-arrow-left"></i>
                    <span>Précédent : Représentation</span>
                </a>
                <a href="exercices.html" class="nav-button">
                    <span>Suivant : Exercices</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Formation Python - Programme Académique Tunisien</p>
            <p>Séance 1 : Complexité Algorithmique</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
