<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exercices Pratiques</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .exercise-container {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            border-left: 5px solid #3498db;
        }
        
        .exercise-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .difficulty-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .facile { background: #27ae60; }
        .moyen { background: #f39c12; }
        .difficile { background: #e74c3c; }
        
        .exercise-content {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 1rem;
        }
        
        .solution-toggle {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }
        
        .solution-toggle:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .solution {
            display: none;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 1rem;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }
        
        .solution.show {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .exercise-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .flowchart-exercise {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
        }
        
        .drawing-area {
            background: white;
            color: #333;
            min-height: 300px;
            border-radius: 10px;
            padding: 2rem;
            margin-top: 1rem;
            border: 2px dashed #bdc3c7;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .tips-section {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
        }
        
        .tip-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid #fff;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1><i class="fas fa-pencil-alt"></i> Exercices Pratiques</h1>
            <h2>Mise en Application des Concepts</h2>
            <div class="session-info">
                <span><i class="fas fa-clock"></i> Partie 4/4</span>
                <span><i class="fas fa-tasks"></i> Pratique</span>
            </div>
        </div>
    </header>

    <nav class="navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link"><i class="fas fa-home"></i> Accueil</a></li>
                <li><a href="#organigrammes" class="nav-link"><i class="fas fa-project-diagram"></i> Organigrammes</a></li>
                <li><a href="#pseudocode" class="nav-link"><i class="fas fa-code"></i> Pseudo-code</a></li>
                <li><a href="#analyse" class="nav-link"><i class="fas fa-chart-line"></i> Analyse</a></li>
                <li><a href="#conseils" class="nav-link"><i class="fas fa-lightbulb"></i> Conseils</a></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <!-- Section Organigrammes -->
            <section id="organigrammes" class="content-section">
                <h2><i class="fas fa-project-diagram"></i> Exercices d'Organigrammes</h2>
                
                <div class="exercise-container">
                    <div class="exercise-header">
                        <h3><i class="fas fa-calculator"></i> Exercice 1 : Calculatrice Simple</h3>
                        <span class="difficulty-badge facile">Facile</span>
                    </div>
                    <div class="exercise-content">
                        <p><strong>Énoncé :</strong> Créez un organigramme pour un algorithme qui :</p>
                        <ul>
                            <li>Lit deux nombres A et B</li>
                            <li>Lit une opération (+, -, *, /)</li>
                            <li>Effectue le calcul correspondant</li>
                            <li>Affiche le résultat</li>
                            <li>Gère le cas de la division par zéro</li>
                        </ul>
                        
                        <button class="solution-toggle" onclick="toggleSolution('solution1')">
                            <i class="fas fa-eye"></i> Voir la Solution
                        </button>
                        
                        <div id="solution1" class="solution">
DÉBUT
  ↓
[Lire A, B, opération]
  ↓
{opération = "+" ?} → OUI → [résultat = A + B] → [Afficher résultat] → FIN
  ↓ NON
{opération = "-" ?} → OUI → [résultat = A - B] → [Afficher résultat] → FIN
  ↓ NON
{opération = "*" ?} → OUI → [résultat = A * B] → [Afficher résultat] → FIN
  ↓ NON
{opération = "/" ?} → OUI → {B ≠ 0 ?} → OUI → [résultat = A / B] → [Afficher résultat] → FIN
                              ↓ NON
                              [Afficher "Division par zéro impossible"] → FIN
  ↓ NON
[Afficher "Opération non reconnue"] → FIN
                        </div>
                    </div>
                </div>

                <div class="exercise-container">
                    <div class="exercise-header">
                        <h3><i class="fas fa-sort-numeric-up"></i> Exercice 2 : Tri de Trois Nombres</h3>
                        <span class="difficulty-badge moyen">Moyen</span>
                    </div>
                    <div class="exercise-content">
                        <p><strong>Énoncé :</strong> Créez un organigramme pour trier trois nombres A, B, C par ordre croissant.</p>
                        
                        <button class="solution-toggle" onclick="toggleSolution('solution2')">
                            <i class="fas fa-eye"></i> Voir la Solution
                        </button>
                        
                        <div id="solution2" class="solution">
DÉBUT
  ↓
[Lire A, B, C]
  ↓
{A > B ?} → OUI → [Échanger A et B]
  ↓ NON      ↓
{A > C ?} → OUI → [Échanger A et C]
  ↓ NON      ↓
{B > C ?} → OUI → [Échanger B et C]
  ↓ NON      ↓
[Afficher A, B, C] (ordre croissant)
  ↓
FIN

Note: Cette solution utilise une approche simple avec des échanges successifs.
Une approche plus optimisée nécessiterait une analyse plus complexe des cas.
                        </div>
                    </div>
                </div>

                <div class="flowchart-exercise">
                    <h3><i class="fas fa-pencil-alt"></i> Exercice Pratique : Dessinez Votre Organigramme</h3>
                    <p>Créez un organigramme pour calculer la moyenne de N notes et déterminer si l'étudiant a réussi (moyenne ≥ 10).</p>
                    
                    <div class="drawing-area">
                        <div>
                            <i class="fas fa-draw-polygon" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 1rem;"></i>
                            <p>Utilisez du papier ou un outil de dessin pour créer votre organigramme</p>
                            <p><strong>Éléments à inclure :</strong></p>
                            <ul style="text-align: left; display: inline-block;">
                                <li>Lecture du nombre de notes N</li>
                                <li>Boucle pour lire les N notes</li>
                                <li>Calcul de la somme</li>
                                <li>Calcul de la moyenne</li>
                                <li>Test de réussite</li>
                                <li>Affichage du résultat</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Pseudo-code -->
            <section id="pseudocode" class="content-section">
                <h2><i class="fas fa-code"></i> Exercices de Pseudo-code</h2>
                
                <div class="exercise-grid">
                    <div class="exercise-container">
                        <div class="exercise-header">
                            <h3><i class="fas fa-hashtag"></i> Exercice 3 : Nombres Premiers</h3>
                            <span class="difficulty-badge moyen">Moyen</span>
                        </div>
                        <div class="exercise-content">
                            <p><strong>Énoncé :</strong> Écrivez un pseudo-code pour vérifier si un nombre N est premier.</p>
                            
                            <button class="solution-toggle" onclick="toggleSolution('solution3')">
                                <i class="fas fa-eye"></i> Voir la Solution
                            </button>
                            
                            <div id="solution3" class="solution">
DÉBUT
    VARIABLE N : entier
    VARIABLE i : entier
    VARIABLE estPremier : booléen
    
    AFFICHER "Entrez un nombre : "
    LIRE N
    
    SI N <= 1 ALORS
        estPremier ← FAUX
    SINON SI N = 2 ALORS
        estPremier ← VRAI
    SINON
        estPremier ← VRAI
        POUR i DE 2 À racine(N) FAIRE
            SI N % i = 0 ALORS
                estPremier ← FAUX
                SORTIR DE LA BOUCLE
            FIN SI
        FIN POUR
    FIN SI
    
    SI estPremier = VRAI ALORS
        AFFICHER N + " est un nombre premier"
    SINON
        AFFICHER N + " n'est pas un nombre premier"
    FIN SI
FIN
                            </div>
                        </div>
                    </div>

                    <div class="exercise-container">
                        <div class="exercise-header">
                            <h3><i class="fas fa-repeat"></i> Exercice 4 : Suite de Fibonacci</h3>
                            <span class="difficulty-badge difficile">Difficile</span>
                        </div>
                        <div class="exercise-content">
                            <p><strong>Énoncé :</strong> Écrivez un pseudo-code pour afficher les N premiers termes de la suite de Fibonacci.</p>
                            
                            <button class="solution-toggle" onclick="toggleSolution('solution4')">
                                <i class="fas fa-eye"></i> Voir la Solution
                            </button>
                            
                            <div id="solution4" class="solution">
DÉBUT
    VARIABLE N : entier
    VARIABLE a, b, suivant : entier
    VARIABLE i : entier
    
    AFFICHER "Combien de termes voulez-vous ? "
    LIRE N
    
    SI N <= 0 ALORS
        AFFICHER "Le nombre doit être positif"
    SINON SI N = 1 ALORS
        AFFICHER "0"
    SINON SI N = 2 ALORS
        AFFICHER "0, 1"
    SINON
        a ← 0
        b ← 1
        AFFICHER a + ", " + b
        
        POUR i DE 3 À N FAIRE
            suivant ← a + b
            AFFICHER ", " + suivant
            a ← b
            b ← suivant
        FIN POUR
    FIN SI
FIN
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Analyse -->
            <section id="analyse" class="content-section">
                <h2><i class="fas fa-chart-line"></i> Exercices d'Analyse de Complexité</h2>
                
                <div class="exercise-container">
                    <div class="exercise-header">
                        <h3><i class="fas fa-search"></i> Exercice 5 : Analyse de Complexité</h3>
                        <span class="difficulty-badge moyen">Moyen</span>
                    </div>
                    <div class="exercise-content">
                        <p><strong>Énoncé :</strong> Analysez la complexité temporelle des algorithmes suivants :</p>
                        
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <strong>Algorithme A :</strong>
                            <pre style="margin: 0.5rem 0;">
POUR i DE 1 À n FAIRE
    AFFICHER i
FIN POUR</pre>
                        </div>
                        
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <strong>Algorithme B :</strong>
                            <pre style="margin: 0.5rem 0;">
POUR i DE 1 À n FAIRE
    POUR j DE 1 À n FAIRE
        AFFICHER i * j
    FIN POUR
FIN POUR</pre>
                        </div>
                        
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <strong>Algorithme C :</strong>
                            <pre style="margin: 0.5rem 0;">
i ← n
TANT QUE i > 1 FAIRE
    AFFICHER i
    i ← i / 2
FIN TANT QUE</pre>
                        </div>
                        
                        <button class="solution-toggle" onclick="toggleSolution('solution5')">
                            <i class="fas fa-eye"></i> Voir la Solution
                        </button>
                        
                        <div id="solution5" class="solution">
Analyse des complexités :

Algorithme A : O(n)
- Une seule boucle qui s'exécute n fois
- Chaque itération fait une opération constante
- Complexité linéaire

Algorithme B : O(n²)
- Deux boucles imbriquées, chacune s'exécutant n fois
- Total : n × n = n² opérations
- Complexité quadratique

Algorithme C : O(log n)
- À chaque itération, i est divisé par 2
- Le nombre d'itérations est log₂(n)
- Complexité logarithmique

Classement par efficacité (du plus rapide au plus lent) :
1. Algorithme C : O(log n) - Le plus efficace
2. Algorithme A : O(n) - Efficacité moyenne
3. Algorithme B : O(n²) - Le moins efficace
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Conseils -->
            <section id="conseils" class="content-section">
                <h2><i class="fas fa-lightbulb"></i> Conseils et Bonnes Pratiques</h2>
                
                <div class="tips-section">
                    <h3><i class="fas fa-star"></i> Conseils pour Réussir</h3>
                    
                    <div class="tip-item">
                        <h4><i class="fas fa-pencil-alt"></i> Pour les Organigrammes</h4>
                        <ul>
                            <li>Commencez toujours par "DÉBUT" et finissez par "FIN"</li>
                            <li>Utilisez les bons symboles pour chaque type d'opération</li>
                            <li>Assurez-vous que chaque chemin mène à une fin</li>
                            <li>Testez votre organigramme avec des exemples</li>
                        </ul>
                    </div>
                    
                    <div class="tip-item">
                        <h4><i class="fas fa-code"></i> Pour le Pseudo-code</h4>
                        <ul>
                            <li>Utilisez une indentation claire pour montrer la structure</li>
                            <li>Employez des noms de variables explicites</li>
                            <li>Commentez les parties complexes</li>
                            <li>Vérifiez la logique étape par étape</li>
                        </ul>
                    </div>
                    
                    <div class="tip-item">
                        <h4><i class="fas fa-chart-line"></i> Pour l'Analyse de Complexité</h4>
                        <ul>
                            <li>Identifiez les boucles et leur nombre d'itérations</li>
                            <li>Concentrez-vous sur le pire des cas</li>
                            <li>Ignorez les constantes et les termes de faible ordre</li>
                            <li>Pensez à l'évolution avec de grandes données</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Navigation -->
            <div class="page-navigation">
                <a href="complexite.html" class="nav-button" style="background: linear-gradient(135deg, #95a5a6, #7f8c8d); margin-right: 1rem;">
                    <i class="fas fa-arrow-left"></i>
                    <span>Précédent : Complexité</span>
                </a>
                <a href="index.html" class="nav-button" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                    <span>Retour à l'Accueil</span>
                    <i class="fas fa-home"></i>
                </a>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Formation Python - Programme Académique Tunisien</p>
            <p>Séance 1 : Exercices Pratiques</p>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        function toggleSolution(solutionId) {
            const solution = document.getElementById(solutionId);
            solution.classList.toggle('show');
            
            const button = solution.previousElementSibling;
            if (solution.classList.contains('show')) {
                button.innerHTML = '<i class="fas fa-eye-slash"></i> Masquer la Solution';
            } else {
                button.innerHTML = '<i class="fas fa-eye"></i> Voir la Solution';
            }
        }
    </script>
</body>
</html>
