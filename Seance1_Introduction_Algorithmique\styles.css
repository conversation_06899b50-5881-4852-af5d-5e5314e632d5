/* Styles CSS pour le cours d'algorithmique */

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.header h2 {
    color: #3498db;
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 400;
}

.session-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.session-info span {
    background: #3498db;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Navigation */
.navigation {
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-menu {
    display: flex;
    justify-content: center;
    list-style: none;
    gap: 1rem;
    flex-wrap: wrap;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    background: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

/* Main Content */
.main-content {
    background: white;
    margin: 2rem 0;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.content-section {
    padding: 3rem 2rem;
    border-bottom: 1px solid #ecf0f1;
}

.content-section:last-child {
    border-bottom: none;
}

.content-section h2 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Objectifs Grid */
.objectives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.objective-card {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.objective-card:hover {
    transform: translateY(-5px);
}

.objective-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.objective-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

/* Definition Box */
.definition-box {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin: 2rem 0;
    text-align: center;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.definition-box h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.definition-text {
    font-size: 1.2rem;
    line-height: 1.8;
}

/* Concept Comparison */
.concept-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.comparison-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    border-left: 5px solid #3498db;
    transition: transform 0.3s ease;
}

.comparison-card:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.comparison-card h4 {
    color: #2c3e50;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.example {
    background: #3498db;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    font-style: italic;
}

/* Real World Examples */
.real-world-examples {
    margin-top: 3rem;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.example-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 2px solid #ecf0f1;
    transition: all 0.3s ease;
}

.example-item:hover {
    border-color: #3498db;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
}

.example-item i {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.example-item h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

/* Characteristics */
.characteristics-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.characteristic-item {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.characteristic-item:hover {
    transform: translateX(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.char-icon {
    background: #3498db;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.char-content h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.char-example {
    background: #3498db;
    color: white;
    padding: 0.8rem;
    border-radius: 8px;
    margin-top: 1rem;
    font-style: italic;
}

/* Page Navigation */
.page-navigation {
    text-align: center;
    padding: 2rem;
}

.nav-button {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
}

.nav-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
}

/* Footer */
.footer {
    background: rgba(44, 62, 80, 0.95);
    color: white;
    text-align: center;
    padding: 2rem 0;
    backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2rem;
    }
    
    .header h2 {
        font-size: 1.4rem;
    }
    
    .session-info {
        gap: 1rem;
    }
    
    .nav-menu {
        gap: 0.5rem;
    }
    
    .nav-link {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .content-section {
        padding: 2rem 1rem;
    }
    
    .objectives-grid {
        grid-template-columns: 1fr;
    }
    
    .concept-comparison {
        grid-template-columns: 1fr;
    }
    
    .characteristic-item {
        flex-direction: column;
        text-align: center;
    }
}
