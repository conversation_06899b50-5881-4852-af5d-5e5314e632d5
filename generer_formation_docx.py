#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour générer un document Word (.docx) de formation Python 20h
Conforme au programme académique tunisien
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def ajouter_titre_principal(doc, texte):
    """Ajoute un titre principal au document"""
    titre = doc.add_heading(texte, 0)
    titre.alignment = WD_ALIGN_PARAGRAPH.CENTER
    return titre

def ajouter_titre_section(doc, texte, niveau=1):
    """Ajoute un titre de section"""
    return doc.add_heading(texte, niveau)

def ajouter_paragraphe_gras(doc, texte):
    """Ajoute un paragraphe en gras"""
    p = doc.add_paragraph()
    run = p.add_run(texte)
    run.bold = True
    return p

def ajouter_liste_puces(doc, elements):
    """Ajoute une liste à puces"""
    for element in elements:
        p = doc.add_paragraph(element, style='List Bullet')

def creer_tableau_planning(doc):
    """Crée le tableau de planning"""
    table = doc.add_table(rows=1, cols=5)
    table.style = 'Table Grid'
    
    # En-têtes
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Semaine'
    hdr_cells[1].text = 'Séance'
    hdr_cells[2].text = 'Durée'
    hdr_cells[3].text = 'Contenu'
    hdr_cells[4].text = 'Type'
    
    # Données du planning
    planning_data = [
        ['1', '1', '2h', 'Introduction Algorithmique', 'Théorie + TP'],
        ['1', '2', '2h', 'Variables et Types', 'Théorie + TP'],
        ['2', '3', '2h', 'Structures Conditionnelles', 'Théorie + TP'],
        ['2', '4', '2h', 'Structures Itératives', 'Théorie + TP'],
        ['3', '5', '2h', 'Tableaux et Listes', 'Théorie + TP'],
        ['3', '6', '2h', 'Fonctions et Procédures', 'Théorie + TP'],
        ['4', '7', '2h', 'Introduction Python', 'Théorie + TP'],
        ['4', '8', '2h', 'Contrôle en Python', 'Théorie + TP'],
        ['5', '9', '2h', 'Structures Python', 'Théorie + TP'],
        ['5', '10', '2h', 'Fonctions Python + Projet', 'TP + Évaluation']
    ]
    
    for row_data in planning_data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = cell_data

def generer_document_formation():
    """Génère le document Word complet de formation"""
    
    # Créer le document
    doc = Document()
    
    # Titre principal
    ajouter_titre_principal(doc, "FORMATION PYTHON - 20 HEURES")
    doc.add_paragraph("Programme de Formation Algorithmique et Python", style='Subtitle')
    doc.add_paragraph("Conforme au Programme Académique Tunisien", style='Subtitle')
    
    # Saut de page
    doc.add_page_break()
    
    # Informations générales
    ajouter_titre_section(doc, "INFORMATIONS GÉNÉRALES")
    
    infos = [
        "Durée totale : 20 heures",
        "Public cible : Débutants en programmation",
        "Prérequis : Notions de base en mathématiques",
        "Objectif : Maîtriser les concepts algorithmiques fondamentaux et leur implémentation en Python"
    ]
    
    for info in infos:
        ajouter_paragraphe_gras(doc, info)
    
    # Structure de la formation
    ajouter_titre_section(doc, "STRUCTURE DE LA FORMATION")
    
    # Phase 1
    ajouter_titre_section(doc, "PHASE 1 : ALGORITHMIQUE DE BASE (8 heures)", 2)
    
    # Séance 1
    ajouter_titre_section(doc, "Séance 1 : Introduction à l'Algorithmique (2h)", 3)
    ajouter_paragraphe_gras(doc, "Objectifs :")
    objectifs_s1 = [
        "Comprendre les concepts de base de l'algorithmique",
        "Distinguer algorithme, programme et langage de programmation"
    ]
    ajouter_liste_puces(doc, objectifs_s1)
    
    ajouter_paragraphe_gras(doc, "Contenu :")
    contenu_s1 = [
        "Définition d'un algorithme",
        "Caractéristiques d'un bon algorithme",
        "Représentation des algorithmes (organigramme, pseudo-code)",
        "Notion de complexité algorithmique (introduction)"
    ]
    ajouter_liste_puces(doc, contenu_s1)
    
    ajouter_paragraphe_gras(doc, "Exercices pratiques :")
    exercices_s1 = [
        "Création d'organigrammes simples",
        "Écriture de pseudo-code pour des problèmes basiques"
    ]
    ajouter_liste_puces(doc, exercices_s1)
    
    # Séance 2
    ajouter_titre_section(doc, "Séance 2 : Variables et Types de Données (2h)", 3)
    ajouter_paragraphe_gras(doc, "Objectifs :")
    objectifs_s2 = [
        "Maîtriser la notion de variable",
        "Comprendre les différents types de données"
    ]
    ajouter_liste_puces(doc, objectifs_s2)
    
    ajouter_paragraphe_gras(doc, "Contenu :")
    contenu_s2 = [
        "Déclaration et affectation de variables",
        "Types de données : entier, réel, caractère, chaîne, booléen",
        "Opérations arithmétiques et logiques",
        "Priorité des opérateurs"
    ]
    ajouter_liste_puces(doc, contenu_s2)
    
    # Séance 3
    ajouter_titre_section(doc, "Séance 3 : Structures de Contrôle - Conditions (2h)", 3)
    ajouter_paragraphe_gras(doc, "Objectifs :")
    objectifs_s3 = [
        "Maîtriser les structures conditionnelles",
        "Implémenter la logique de décision"
    ]
    ajouter_liste_puces(doc, objectifs_s3)
    
    ajouter_paragraphe_gras(doc, "Contenu :")
    contenu_s3 = [
        "Structure SI...ALORS...SINON",
        "Conditions composées (ET, OU, NON)",
        "Structures conditionnelles imbriquées",
        "Structure SELON (switch/case)"
    ]
    ajouter_liste_puces(doc, contenu_s3)
    
    # Séance 4
    ajouter_titre_section(doc, "Séance 4 : Structures de Contrôle - Boucles (2h)", 3)
    ajouter_paragraphe_gras(doc, "Objectifs :")
    objectifs_s4 = [
        "Maîtriser les structures itératives",
        "Optimiser les algorithmes répétitifs"
    ]
    ajouter_liste_puces(doc, objectifs_s4)
    
    ajouter_paragraphe_gras(doc, "Contenu :")
    contenu_s4 = [
        "Boucle POUR (for)",
        "Boucle TANT QUE (while)",
        "Boucle RÉPÉTER...JUSQU'À",
        "Boucles imbriquées",
        "Contrôle de boucles (break, continue)"
    ]
    ajouter_liste_puces(doc, contenu_s4)
    
    # Nouvelle page pour Phase 2
    doc.add_page_break()
    
    # Phase 2
    ajouter_titre_section(doc, "PHASE 2 : STRUCTURES DE DONNÉES (4 heures)", 2)
    
    # Séance 5
    ajouter_titre_section(doc, "Séance 5 : Tableaux et Listes (2h)", 3)
    ajouter_paragraphe_gras(doc, "Contenu :")
    contenu_s5 = [
        "Déclaration et initialisation de tableaux",
        "Parcours de tableaux",
        "Recherche dans un tableau",
        "Tri de tableaux (tri à bulles, tri par sélection)"
    ]
    ajouter_liste_puces(doc, contenu_s5)
    
    # Séance 6
    ajouter_titre_section(doc, "Séance 6 : Fonctions et Procédures (2h)", 3)
    ajouter_paragraphe_gras(doc, "Contenu :")
    contenu_s6 = [
        "Définition et appel de fonctions",
        "Paramètres et valeurs de retour",
        "Portée des variables (locale/globale)",
        "Récursivité (introduction)"
    ]
    ajouter_liste_puces(doc, contenu_s6)
    
    # Phase 3
    ajouter_titre_section(doc, "PHASE 3 : IMPLÉMENTATION EN PYTHON (8 heures)", 2)
    
    # Séances 7-10 (résumé)
    seances_python = [
        ("Séance 7 : Introduction à Python (2h)", [
            "Installation et configuration de Python",
            "Environnement de développement (IDLE, VS Code)",
            "Syntaxe de base Python",
            "Variables et types de données en Python"
        ]),
        ("Séance 8 : Structures de Contrôle en Python (2h)", [
            "Instructions if, elif, else",
            "Boucles for et while en Python",
            "Compréhensions de listes (introduction)",
            "Gestion des exceptions (try/except)"
        ]),
        ("Séance 9 : Structures de Données Python (2h)", [
            "Listes, tuples, dictionnaires, ensembles",
            "Méthodes et opérations sur les structures",
            "Chaînes de caractères avancées",
            "Fichiers texte (lecture/écriture)"
        ]),
        ("Séance 10 : Fonctions et Modules Python (2h)", [
            "Définition de fonctions en Python",
            "Arguments par défaut et arguments nommés",
            "Fonctions lambda",
            "Modules et packages"
        ])
    ]
    
    for titre_seance, contenu_seance in seances_python:
        ajouter_titre_section(doc, titre_seance, 3)
        ajouter_paragraphe_gras(doc, "Contenu :")
        ajouter_liste_puces(doc, contenu_seance)
    
    # Nouvelle page pour évaluation
    doc.add_page_break()
    
    # Évaluation
    ajouter_titre_section(doc, "ÉVALUATION ET PROJETS")
    
    ajouter_titre_section(doc, "Évaluations Continues :", 2)
    evaluations = [
        "Quiz algorithmique (Séance 4) - 20%",
        "Projet Python intermédiaire (Séance 8) - 30%",
        "Projet final (Séance 10) - 50%"
    ]
    ajouter_liste_puces(doc, evaluations)
    
    ajouter_titre_section(doc, "Projet Final Suggéré :", 2)
    ajouter_paragraphe_gras(doc, "Système de Gestion d'une Bibliothèque")
    projet_final = [
        "Gestion des livres (ajout, suppression, recherche)",
        "Gestion des emprunts",
        "Statistiques et rapports",
        "Interface utilisateur en mode console"
    ]
    ajouter_liste_puces(doc, projet_final)
    
    # Planning détaillé
    ajouter_titre_section(doc, "PLANNING DÉTAILLÉ")
    creer_tableau_planning(doc)
    
    # Ressources
    ajouter_titre_section(doc, "RESSOURCES PÉDAGOGIQUES")
    
    ajouter_titre_section(doc, "Manuels de Référence :", 2)
    manuels = [
        "\"Algorithmique et Programmation\" - Programme Tunisien",
        "\"Python pour les Débutants\" - Documentation officielle",
        "Exercices du Baccalauréat Tunisien (Section Informatique)"
    ]
    ajouter_liste_puces(doc, manuels)
    
    ajouter_titre_section(doc, "Outils Nécessaires :", 2)
    outils = [
        "Python 3.8+ (dernière version stable)",
        "Éditeur de code (VS Code, PyCharm Community, ou IDLE)",
        "Accès internet pour documentation"
    ]
    ajouter_liste_puces(doc, outils)
    
    # Critères de réussite
    ajouter_titre_section(doc, "CRITÈRES DE RÉUSSITE")
    
    ajouter_titre_section(doc, "Compétences Algorithmiques :", 2)
    comp_algo = [
        "Analyser un problème et concevoir un algorithme",
        "Utiliser les structures de contrôle appropriées",
        "Optimiser la complexité des algorithmes",
        "Documenter et commenter le code"
    ]
    ajouter_liste_puces(doc, comp_algo)
    
    ajouter_titre_section(doc, "Compétences Python :", 2)
    comp_python = [
        "Écrire du code Python syntaxiquement correct",
        "Utiliser les structures de données appropriées",
        "Créer et utiliser des fonctions",
        "Déboguer et tester le code"
    ]
    ajouter_liste_puces(doc, comp_python)
    
    # Pied de page
    doc.add_paragraph()
    footer = doc.add_paragraph("Document préparé conformément au programme académique tunisien")
    footer.alignment = WD_ALIGN_PARAGRAPH.CENTER
    footer_version = doc.add_paragraph("Version 1.0 - Formation Python 20h")
    footer_version.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Sauvegarder le document
    doc.save('Formation_Python_20h_Programme.docx')
    print("✅ Document Word créé avec succès : Formation_Python_20h_Programme.docx")

if __name__ == "__main__":
    try:
        generer_document_formation()
    except ImportError:
        print("❌ Erreur : La bibliothèque python-docx n'est pas installée.")
        print("📦 Installez-la avec : pip install python-docx")
    except Exception as e:
        print(f"❌ Erreur lors de la génération du document : {e}")
