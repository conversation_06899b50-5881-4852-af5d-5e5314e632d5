// Script JavaScript pour le cours d'algorithmique

// Navigation fluide
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling pour les liens de navigation
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 100; // Offset pour la navigation fixe
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // Mise en surbrillance du lien actif
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });
    
    // Mise en surbrillance automatique lors du scroll
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('.content-section');
        const scrollPos = window.scrollY + 150;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            
            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${sectionId}`) {
                        link.classList.add('active');
                    }
                });
            }
        });
    });
    
    // Animation d'apparition des cartes
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observer les éléments à animer
    const animatedElements = document.querySelectorAll('.objective-card, .comparison-card, .example-item, .characteristic-item, .complexity-card');
    
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
    
    // Effet de typing pour les définitions
    const definitionTexts = document.querySelectorAll('.definition-text');
    
    definitionTexts.forEach(text => {
        const originalText = text.textContent;
        text.textContent = '';
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    typeText(text, originalText, 30);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(text);
    });
    
    // Compteurs animés pour les exemples de complexité
    const counters = document.querySelectorAll('.comparison-table td');
    
    counters.forEach(counter => {
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const finalValue = parseInt(counter.textContent.replace(/,/g, ''));
                    if (!isNaN(finalValue) && finalValue > 1) {
                        animateCounter(counter, finalValue);
                    }
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(counter);
    });
});

// Fonction de typing effect
function typeText(element, text, speed) {
    let i = 0;
    const timer = setInterval(function() {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
        } else {
            clearInterval(timer);
        }
    }, speed);
}

// Animation des compteurs
function animateCounter(element, finalValue) {
    const duration = 2000; // 2 secondes
    const startTime = performance.now();
    const startValue = 0;
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Fonction d'easing pour un effet plus naturel
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(startValue + (finalValue - startValue) * easeOutQuart);
        
        // Formatage avec virgules pour les grands nombres
        element.textContent = currentValue.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = finalValue.toLocaleString();
        }
    }
    
    requestAnimationFrame(updateCounter);
}

// Gestion des tooltips pour les badges de complexité
document.addEventListener('DOMContentLoaded', function() {
    const complexityBadges = document.querySelectorAll('.complexity-badge');
    
    complexityBadges.forEach(badge => {
        badge.addEventListener('mouseenter', function() {
            const tooltip = createTooltip(this);
            document.body.appendChild(tooltip);
            positionTooltip(this, tooltip);
        });
        
        badge.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.complexity-tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        });
    });
});

// Création de tooltips informatifs
function createTooltip(element) {
    const tooltip = document.createElement('div');
    tooltip.className = 'complexity-tooltip';
    tooltip.style.cssText = `
        position: absolute;
        background: #2c3e50;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        font-size: 0.8rem;
        z-index: 1000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    const badgeText = element.textContent.trim();
    let tooltipText = '';
    
    switch(badgeText) {
        case 'O(1)':
        case 'Excellent':
            tooltipText = 'Temps constant - Performance optimale';
            break;
        case 'O(log n)':
        case 'Très bon':
            tooltipText = 'Temps logarithmique - Très efficace';
            break;
        case 'O(n)':
        case 'Acceptable':
            tooltipText = 'Temps linéaire - Performance raisonnable';
            break;
        case 'O(n²)':
        case 'Problématique':
            tooltipText = 'Temps quadratique - À éviter pour de grandes données';
            break;
        default:
            tooltipText = 'Information sur la complexité';
    }
    
    tooltip.textContent = tooltipText;
    
    // Animation d'apparition
    setTimeout(() => {
        tooltip.style.opacity = '1';
    }, 10);
    
    return tooltip;
}

// Positionnement des tooltips
function positionTooltip(element, tooltip) {
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    
    tooltip.style.left = (rect.left + rect.width / 2 - tooltipRect.width / 2) + 'px';
    tooltip.style.top = (rect.top - tooltipRect.height - 10) + 'px';
}

// Gestion du mode sombre (bonus)
function toggleDarkMode() {
    document.body.classList.toggle('dark-mode');
    localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
}

// Restauration du mode sombre
document.addEventListener('DOMContentLoaded', function() {
    if (localStorage.getItem('darkMode') === 'true') {
        document.body.classList.add('dark-mode');
    }
});

// Gestion des raccourcis clavier
document.addEventListener('keydown', function(e) {
    // Ctrl + D pour le mode sombre
    if (e.ctrlKey && e.key === 'd') {
        e.preventDefault();
        toggleDarkMode();
    }
    
    // Flèches pour navigation
    if (e.key === 'ArrowLeft') {
        const prevButton = document.querySelector('.nav-button[href*="Précédent"], .nav-button[href*="précédent"]');
        if (prevButton) {
            window.location.href = prevButton.href;
        }
    }
    
    if (e.key === 'ArrowRight') {
        const nextButton = document.querySelector('.nav-button[href*="Suivant"], .nav-button[href*="suivant"]');
        if (nextButton) {
            window.location.href = nextButton.href;
        }
    }
});

// Fonction utilitaire pour déboguer
function debugInfo() {
    console.log('🎓 Cours d\'Algorithmique - Séance 1');
    console.log('📊 Statistiques de la page:');
    console.log('- Sections:', document.querySelectorAll('.content-section').length);
    console.log('- Cartes d\'objectifs:', document.querySelectorAll('.objective-card').length);
    console.log('- Exemples:', document.querySelectorAll('.example-item').length);
    console.log('- Exercices:', document.querySelectorAll('.exercise-container').length);
}

// Appel de debug en mode développement
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    debugInfo();
}
